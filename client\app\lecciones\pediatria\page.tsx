"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import CasosClinicosUI from "@/components/casosclinicos_ui";

// Define interfaces for the content types
interface ContentItem {
  id: number;
  title: string;
  tema: string;
  sistema: string;
}

interface ImageData {
  id: string;
  url: string;
  filename?: string;
  file_path?: string;
}

interface OpcionData {
  id: string;
  opcion: string;
  es_correcta: boolean;
  explicacion: string;
  imagen_url?: string;
  imagen_path?: string;
}

interface PreguntaData {
  id: string;
  caso_clinico: string;
  pregunta: string;
  imagen_url?: string;
  imagen_path?: string;
  opciones: OpcionData[];
  respuesta_correcta: string;
  explicacion_general: string;
}

interface CasoClinico {
  id: number;
  title: string;
  especialidad: string;
  sistema: string;
  tema: string;
  descripcion: string;
  images?: ImageData[];
  preguntas: PreguntaData[];
  created_at: string;
}

const especialidad = "Pediatria";

// Helper function to get API base URL (same pattern as upload components)
function getApiBaseUrl(): string {
  const base = process.env.NEXT_PUBLIC_API_BASE_URL?.replace(/\/$/, '');
  return base || 'http://localhost:8000';
}

async function fetchContent<T>(contentType: string): Promise<T[]> {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/api/v1/contenido/${contentType}/?especialidad=${especialidad}`
    );
    if (!response.ok) {
      if (response.status === 404) {
        // Return empty array if endpoint doesn't exist yet
        return [];
      }
      throw new Error(`Failed to fetch ${contentType}: ${response.status} ${response.statusText}`);
    }
    return response.json();
  } catch (error) {
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error(`Network error while fetching ${contentType}. Make sure the server is running.`);
    }
    throw error;
  }
}

function ContentSection<T extends ContentItem>({
  title,
  data,
  basePath,
  onSpecialView,
}: {
  title: string;
  data: T[] | null;
  basePath: string;
  onSpecialView?: () => void;
}) {
  if (!data || data.length === 0) {
    return null;
  }

  return (
    <section className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-semibold">{title}</h2>
        {onSpecialView && (
          <button
            onClick={onSpecialView}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
          >
            Ver en modo interactivo
          </button>
        )}
      </div>
      <ul className="space-y-2">
        {data.map((item) => (
          <li key={item.id} className="p-2 border rounded hover:bg-gray-100">
            <Link href={`/lecciones/pediatria/${basePath}/${item.id}`}>
              <span className="font-medium">{item.title}</span> -{" "}
              <span className="text-sm text-gray-600">
                {item.sistema} / {item.tema}
              </span>
            </Link>
          </li>
        ))}
      </ul>
    </section>
  );
}

export default function PediatriaPage() {
  const [content, setContent] = useState<{ [key: string]: ContentItem[] | null }>({
    videoclases: null,
    videos_cortos: null,
    notas_clinicas: null,
    casos_clinicos: null,
    cuestionarios: null,
    flashcards: null,
    repaso: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewingCasosClinicosUI, setViewingCasosClinicosUI] = useState(false);

  useEffect(() => {
    async function loadContent() {
      try {
        const contentTypes = [
          "videoclases",
          "videos-cortos",
          "notas-clinicas",
          "casos-clinicos",
          "cuestionarios",
          "flashcards",
          "repaso",
        ];

        // Use Promise.allSettled to handle individual failures gracefully
        const results = await Promise.allSettled(
          contentTypes.map(type => fetchContent<ContentItem>(type))
        );

        const newContent: { [key: string]: ContentItem[] | null } = {};
        const errors: string[] = [];

        contentTypes.forEach((type, index) => {
          const result = results[index];
          if (result.status === 'fulfilled') {
            newContent[type.replace(/-/g, '_')] = result.value;
          } else {
            newContent[type.replace(/-/g, '_')] = [];
            errors.push(`${type}: ${result.reason.message}`);
          }
        });

        setContent(newContent);

        // Only show error if all requests failed
        if (errors.length === contentTypes.length) {
          setError(`Failed to load content: ${errors.join(', ')}`);
        } else if (errors.length > 0) {
          console.warn('Some content types failed to load:', errors);
        }

      } catch (err) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError("An unknown error occurred");
        }
      } finally {
        setLoading(false);
      }
    }

    loadContent();
  }, []);

  if (loading) {
    return <div className="container mx-auto p-4">Loading content...</div>;
  }

  if (error) {
    return <div className="container mx-auto p-4">Error: {error}</div>;
  }

  // Check if any content is available
  const hasAnyContent = Object.values(content).some(data => data && data.length > 0);

  // Show interactive clinical cases UI
  if (viewingCasosClinicosUI) {
    return (
      <div className="container mx-auto p-4">
        <CasosClinicosUI
          casos={content.casos_clinicos as CasoClinico[] || []}
          onBack={() => setViewingCasosClinicosUI(false)}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Lecciones de Pediatría</h1>

      {!hasAnyContent && !loading && !error && (
        <div className="text-center py-8">
          <p className="text-gray-600 text-lg">
            No hay contenido disponible para Pediatría en este momento.
          </p>
          <p className="text-gray-500 text-sm mt-2">
            El contenido se mostrará aquí una vez que sea subido al sistema.
          </p>
        </div>
      )}

      <ContentSection
        title="Videoclases"
        data={content.videoclases}
        basePath="videoclases"
      />
      <ContentSection
        title="Videos Cortos"
        data={content.videos_cortos}
        basePath="videos-cortos"
      />
      <ContentSection
        title="Notas Clínicas"
        data={content.notas_clinicas}
        basePath="notas-clinicas"
      />
      <ContentSection
        title="Casos Clínicos"
        data={content.casos_clinicos}
        basePath="casos-clinicos"
        onSpecialView={() => setViewingCasosClinicosUI(true)}
      />
      <ContentSection
        title="Cuestionarios"
        data={content.cuestionarios}
        basePath="cuestionarios"
      />
      <ContentSection
        title="Flashcards"
        data={content.flashcards}
        basePath="flashcards"
      />
      <ContentSection
        title="Repaso"
        data={content.repaso}
        basePath="repaso"
      />
    </div>
  );
}