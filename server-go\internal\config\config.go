package config

import (
	"os"
	"strconv"
	"strings"
)

// Settings holds all configuration for the application
type Settings struct {
	// Project info
	ProjectName string
	Version     string
	APIV1Str    string
	Environment string
	Port        string

	// CORS
	BackendCorsOrigins []string

	// Google Cloud Storage Configuration
	GoogleCloudProjectID     string
	GoogleCloudPrivateKeyID  string
	GoogleCloudPrivateKey    string
	GoogleCloudClientEmail   string
	GoogleCloudClientID      string
	GoogleCloudClientCertURL string
	GoogleCloudStorageBucket string
	GCSSignedURLExpiration   int

	// Database
	DatabaseURL string
}

// Load reads configuration from environment variables
func Load() *Settings {
	return &Settings{
		// Project info
		ProjectName: getEnv("PROJECT_NAME", "TuResiBo API"),
		Version:     getEnv("VERSION", "0.1.0"),
		APIV1Str:    getEnv("API_V1_STR", "/api/v1"),
		Environment: getEnv("ENVIRONMENT", "local"),
		Port:        getEnv("PORT", "8000"),

		// CORS
		BackendCorsOrigins: getEnvSlice("BACKEND_CORS_ORIGINS", []string{
			"http://localhost:3000",
			"http://127.0.0.1:3000",
		}),

		// Google Cloud Storage Configuration
		GoogleCloudProjectID:     getEnv("GOOGLE_CLOUD_PROJECT_ID", ""),
		GoogleCloudPrivateKeyID:  getEnv("GOOGLE_CLOUD_PRIVATE_KEY_ID", ""),
		GoogleCloudPrivateKey:    getEnv("GOOGLE_CLOUD_PRIVATE_KEY", ""),
		GoogleCloudClientEmail:   getEnv("GOOGLE_CLOUD_CLIENT_EMAIL", ""),
		GoogleCloudClientID:      getEnv("GOOGLE_CLOUD_CLIENT_ID", ""),
		GoogleCloudClientCertURL: getEnv("GOOGLE_CLOUD_CLIENT_CERT_URL", ""),
		GoogleCloudStorageBucket: getEnv("GOOGLE_CLOUD_STORAGE_BUCKET", ""),
		GCSSignedURLExpiration:   getEnvInt("GCS_SIGNED_URL_EXPIRATION", 300),

		// Database
		DatabaseURL: getEnv("POSTGRES_DATABASE", ""),
	}
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvInt gets an environment variable as integer with a default value
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvSlice gets an environment variable as slice with a default value
func getEnvSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

// IsProduction returns true if the environment is production
func (s *Settings) IsProduction() bool {
	return s.Environment == "production"
}

// IsLocal returns true if the environment is local
func (s *Settings) IsLocal() bool {
	return s.Environment == "local"
}

// IsStaging returns true if the environment is staging
func (s *Settings) IsStaging() bool {
	return s.Environment == "staging"
}
